#include "_IQNdiv_mathacl.h"

/**
 * @brief Divides two values of IQ31 format, using MathACL
 *
 * @param a             IQ31 type value numerator to be divided.
 * @param b             IQ31 type value denominator to divide by.
 *
 * @return              IQ31 type result of the multiplication. 
 */
int32_t _IQ31div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 31);
}
/**
 * @brief Divides two values of IQ30 format, using MathACL
 *
 * @param a             IQ30 type value numerator to be divided.
 * @param b             IQ30 type value denominator to divide by.
 *
 * @return              IQ30 type result of the multiplication. 
 */
int32_t _IQ30div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 30);
}
/**
 * @brief Divides two values of IQ29 format, using MathACL
 *
 * @param a             IQ29 type value numerator to be divided.
 * @param b             IQ29 type value denominator to divide by.
 *
 * @return              IQ29 type result of the multiplication. 
 */
int32_t _IQ29div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 29);
}
/**
 * @brief Divides two values of IQ28 format, using MathACL
 *
 * @param a             IQ28 type value numerator to be divided.
 * @param b             IQ28 type value denominator to divide by.
 *
 * @return              IQ28 type result of the multiplication. 
 */
int32_t _IQ28div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 28);
}
/**
 * @brief Divides two values of IQ27 format, using MathACL
 *
 * @param a             IQ27 type value numerator to be divided.
 * @param b             IQ27 type value denominator to divide by.
 *
 * @return              IQ27 type result of the multiplication. 
 */
int32_t _IQ27div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 27);
}
/**
 * @brief Divides two values of IQ26 format, using MathACL
 *
 * @param a             IQ26 type value numerator to be divided.
 * @param b             IQ26 type value denominator to divide by.
 *
 * @return              IQ26 type result of the multiplication. 
 */
int32_t _IQ26div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 26);
}
/**
 * @brief Divides two values of IQ25 format, using MathACL
 *
 * @param a             IQ25 type value numerator to be divided.
 * @param b             IQ25 type value denominator to divide by.
 *
 * @return              IQ25 type result of the multiplication. 
 */
int32_t _IQ25div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 25);
}
/**
 * @brief Divides two values of IQ24 format, using MathACL
 *
 * @param a             IQ24 type value numerator to be divided.
 * @param b             IQ24 type value denominator to divide by.
 *
 * @return              IQ24 type result of the multiplication. 
 */
int32_t _IQ24div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 24);
}
/**
 * @brief Divides two values of IQ23 format, using MathACL
 *
 * @param a             IQ23 type value numerator to be divided.
 * @param b             IQ23 type value denominator to divide by.
 *
 * @return              IQ23 type result of the multiplication. 
 */
int32_t _IQ23div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 23);
}
/**
 * @brief Divides two values of IQ22 format, using MathACL
 *
 * @param a             IQ22 type value numerator to be divided.
 * @param b             IQ22 type value denominator to divide by.
 *
 * @return              IQ22 type result of the multiplication. 
 */
int32_t _IQ22div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 22);
}
/**
 * @brief Divides two values of IQ21 format, using MathACL
 *
 * @param a             IQ21 type value numerator to be divided.
 * @param b             IQ21 type value denominator to divide by.
 *
 * @return              IQ21 type result of the multiplication. 
 */
int32_t _IQ21div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 21);
}
/**
 * @brief Divides two values of IQ20 format, using MathACL
 *
 * @param a             IQ20 type value numerator to be divided.
 * @param b             IQ20 type value denominator to divide by.
 *
 * @return              IQ20 type result of the multiplication. 
 */
int32_t _IQ20div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 20);
}
/**
 * @brief Divides two values of IQ19 format, using MathACL
 *
 * @param a             IQ19 type value numerator to be divided.
 * @param b             IQ19 type value denominator to divide by.
 *
 * @return              IQ19 type result of the multiplication. 
 */
int32_t _IQ19div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 19);
}
/**
 * @brief Divides two values of IQ18 format, using MathACL
 *
 * @param a             IQ18 type value numerator to be divided.
 * @param b             IQ18 type value denominator to divide by.
 *
 * @return              IQ18 type result of the multiplication. 
 */
int32_t _IQ18div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 18);
}
/**
 * @brief Divides two values of IQ17 format, using MathACL
 *
 * @param a             IQ17 type value numerator to be divided.
 * @param b             IQ17 type value denominator to divide by.
 *
 * @return              IQ17 type result of the multiplication. 
 */
int32_t _IQ17div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 17);
}
/**
 * @brief Divides two values of IQ16 format, using MathACL
 *
 * @param a             IQ16 type value numerator to be divided.
 * @param b             IQ16 type value denominator to divide by.
 *
 * @return              IQ16 type result of the multiplication. 
 */
int32_t _IQ16div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 16);
}
/**
 * @brief Divides two values of IQ15 format, using MathACL
 *
 * @param a             IQ15 type value numerator to be divided.
 * @param b             IQ15 type value denominator to divide by.
 *
 * @return              IQ15 type result of the multiplication. 
 */
int32_t _IQ15div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 15);
}
/**
 * @brief Divides two values of IQ14 format, using MathACL
 *
 * @param a             IQ14 type value numerator to be divided.
 * @param b             IQ14 type value denominator to divide by.
 *
 * @return              IQ14 type result of the multiplication. 
 */
int32_t _IQ14div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 14);
}
/**
 * @brief Divides two values of IQ13 format, using MathACL
 *
 * @param a             IQ13 type value numerator to be divided.
 * @param b             IQ13 type value denominator to divide by.
 *
 * @return              IQ13 type result of the multiplication. 
 */
int32_t _IQ13div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 13);
}
/**
 * @brief Divides two values of IQ12 format, using MathACL
 *
 * @param a             IQ12 type value numerator to be divided.
 * @param b             IQ12 type value denominator to divide by.
 *
 * @return              IQ12 type result of the multiplication. 
 */
int32_t _IQ12div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 12);
}
/**
 * @brief Divides two values of IQ11 format, using MathACL
 *
 * @param a             IQ11 type value numerator to be divided.
 * @param b             IQ11 type value denominator to divide by.
 *
 * @return              IQ11 type result of the multiplication. 
 */
int32_t _IQ11div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 11);
}
/**
 * @brief Divides two values of IQ10 format, using MathACL
 *
 * @param a             IQ10 type value numerator to be divided.
 * @param b             IQ10 type value denominator to divide by.
 *
 * @return              IQ10 type result of the multiplication. 
 */
int32_t _IQ10div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 10);
}
/**
 * @brief Divides two values of IQ9 format, using MathACL
 *
 * @param a             IQ9 type value numerator to be divided.
 * @param b             IQ9 type value denominator to divide by.
 *
 * @return              IQ9 type result of the multiplication. 
 */
int32_t _IQ9div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 9);
}
/**
 * @brief Divides two values of IQ8 format, using MathACL
 *
 * @param a             IQ8 type value numerator to be divided.
 * @param b             IQ8 type value denominator to divide by.
 *
 * @return              IQ8 type result of the multiplication. 
 */
int32_t _IQ8div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 8);
}
/**
 * @brief Divides two values of IQ7 format, using MathACL
 *
 * @param a             IQ7 type value numerator to be divided.
 * @param b             IQ7 type value denominator to divide by.
 *
 * @return              IQ7 type result of the multiplication. 
 */
int32_t _IQ7div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 7);
}
/**
 * @brief Divides two values of IQ6 format, using MathACL
 *
 * @param a             IQ6 type value numerator to be divided.
 * @param b             IQ6 type value denominator to divide by.
 *
 * @return              IQ6 type result of the multiplication. 
 */
int32_t _IQ6div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 6);
}
/**
 * @brief Divides two values of IQ5 format, using MathACL
 *
 * @param a             IQ5 type value numerator to be divided.
 * @param b             IQ5 type value denominator to divide by.
 *
 * @return              IQ5 type result of the multiplication. 
 */
int32_t _IQ5div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 5);
}
/**
 * @brief Divides two values of IQ4 format, using MathACL
 *
 * @param a             IQ4 type value numerator to be divided.
 * @param b             IQ4 type value denominator to divide by.
 *
 * @return              IQ4 type result of the multiplication. 
 */
int32_t _IQ4div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 4);
}
/**
 * @brief Divides two values of IQ3 format, using MathACL
 *
 * @param a             IQ3 type value numerator to be divided.
 * @param b             IQ3 type value denominator to divide by.
 *
 * @return              IQ3 type result of the multiplication. 
 */
int32_t _IQ3div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 3);
}
/**
 * @brief Divides two values of IQ2 format, using MathACL
 *
 * @param a             IQ2 type value numerator to be divided.
 * @param b             IQ2 type value denominator to divide by.
 *
 * @return              IQ2 type result of the multiplication. 
 */
int32_t _IQ2div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 2);
}
/**
 * @brief Divides two values of IQ1 format, using MathACL
 *
 * @param a             IQ1 type value numerator to be divided.
 * @param b             IQ1 type value denominator to divide by.
 *
 * @return              IQ1 type result of the multiplication. 
 */
int32_t _IQ1div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 1);
}
/**
 * @brief Divides two values of IQ0 format, using MathACL
 *
 * @param a             IQ0 type value numerator to be divided.
 * @param b             IQ0 type value denominator to divide by.
 *
 * @return              IQ0 type result of the multiplication. 
 */
int32_t _IQ0div_mathacl(int32_t a, int32_t b)
{
    return __IQNdiv_mathacl(a ,b, 0);
}
