%%{
// For gcc/default
let Common = system.getScript("/ti/driverlib/Common.js");
let MigrationCommon = system.getScript("/ti/project_config/Common.js");
let Options = system.getScript("/ti/project_config/linker/LINKERMSPM0options.js");

let MigrationController = system.modules["/ti/project_config/ProjectConfig"];
let migration_inst = MigrationController.$static;

%%}
/*****************************************************************************

  Copyright (C) 2023 Texas Instruments Incorporated - http://www.ti.com/

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

   Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

   Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the
   distribution.

   Neither the name of Texas Instruments Incorporated nor the names of
   its contributors may be used to endorse or promote products derived
   from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

*****************************************************************************/
/*
 *  ======== board.cmd.genlibs ========
 *  Libraries needed to link this application's configuration
 *
 *  NOTE, this feature requires software components configured in your
 *  system to correctly indicate their dependencies and report the
 *  libraries needed for your specific configuration.  If you find
 *  errors, please report them on TI's E2E forums
 *  (https://e2e.ti.com/) so they can be addressed in a future
 *  release.
 *
 *  This file allows one to portably link applications that use SysConfig
 *  _without_ having to make changes to build rules when moving to a new
 *  device OR when upgrading to a new version of a SysConfig enabled
 *  product.
 *
 *  DO NOT EDIT - This file is generated by the SysConfig tool for the
 *                TI C/C++ toolchain
 */
% if(migration_inst.genLibDrivers){
INPUT("ti/drivers/lib/gcc/m0p/drivers_`Common.getDeviceFamily().toLowerCase()`.a")
% }
% if(migration_inst.genLibGC){
INPUT("ti/gui_composer/lib/gcc/m0p/gui_composer.a")
% }
% if(migration_inst.genLibMC){
INPUT("ti/motor_control_bldc_sensorless_foc/lib/gcc/m0p/estimator.a")
% }
% if(migration_inst.genLibIQ && migration_inst.genLibIQVersion == "RTS"){
INPUT("ti/iqmath/lib/gcc/m0p/rts/iqmath.a")
% }
% if(migration_inst.genLibIQ && migration_inst.genLibIQVersion == "MATHACL"){
INPUT("ti/iqmath/lib/gcc/m0p/mathacl/iqmath.a")
% }
% if(migration_inst.genLibPMBUS){
INPUT("ti/pmbus/lib/gcc/m0p/pmbus.a")
% }
% if(migration_inst.genLibSMBUS){
INPUT("ti/smbus/lib/gcc/m0p/smbus.a")
% }
% if(migration_inst.genLibCMSIS){
INPUT("third_party/CMSIS/DSP/lib/gcc/m0p/arm_cortexM0l_math.a")
% }
% if(migration_inst.genLibDL){
INPUT("ti/driverlib/lib/gcc/m0p/`Common.getDeviceFamily().toLowerCase()`/driverlib.a")
% }
