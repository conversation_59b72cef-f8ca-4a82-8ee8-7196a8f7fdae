/*
 * Copyright (c) 2024, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
/*!****************************************************************************
 *  @file       services.h
 *  @brief      Application Interface services Module
 *
 * 
 *  @anchor services_h
 *  # Overview
 *
 *  Apis for updating the application interface
 *
 *  <hr>
 ******************************************************************************/

#ifndef SERVICES_H
#define SERVICES_H

#ifdef __cplusplus
extern "C" {
#endif

#include "appInputCtrlInterface.h"
#include "main.h"
#include "appDefs.h"
#include "appUserInputsInterface.h"
#include "appUserInputs.h"

/**
 * @brief     Gets direction status
 * @return    Returns the state of direction
 * 
 * @retval     true  Direction sequence ABC
 * @retval     false Direction sequence ACB
 */
_Bool update_DirectionStatus(void);

/**
 * @brief     Gets brake status
 * @return    Returns the state of brake
 * 
 * @retval     true  brake commanded
 * @retval     false brake not commanded
 */
_Bool update_BrakeStatus(void);

/**
 * @brief     Set periperal 1 config
 */
void peri1Config(void);

/**
 * @brief     Initializes Current Sense Amplifier configurations
 * @param[in] pMotorInputs   Pointer to motor input
 */
void CurrSenseAmpConfigInit(HAL_MEASURE_MOTOR_INPUTS_T *pMotorInputs);

#ifdef __cplusplus
}
#endif
#endif /* SERVICES_H */
